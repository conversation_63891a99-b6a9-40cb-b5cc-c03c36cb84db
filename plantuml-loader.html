<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PlantUML Editor Loader</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        height: 100vh;
        overflow: hidden;
      }

      /* Loading Screen Styles */
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        transition: opacity 0.5s ease-out;
      }

      .loading-screen.hidden {
        opacity: 0;
        pointer-events: none;
      }

      .loading-content {
        text-align: center;
        color: white;
      }

      .loading-title {
        font-size: 2.5rem;
        font-weight: 300;
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .loading-subtitle {
        font-size: 1.2rem;
        margin-bottom: 2rem;
        opacity: 0.9;
      }

      /* Spinner Animation */
      .spinner {
        width: 60px;
        height: 60px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 2rem;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .progress-bar {
        width: 300px;
        height: 4px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
        overflow: hidden;
        margin: 1rem auto;
      }

      .progress-fill {
        height: 100%;
        background: white;
        border-radius: 2px;
        width: 0%;
        transition: width 0.3s ease;
      }

      .loading-status {
        font-size: 0.9rem;
        opacity: 0.8;
        margin-top: 1rem;
      }

      /* Hidden iframe for loading PlantUML */
      .hidden-iframe {
        position: absolute;
        top: -9999px;
        left: -9999px;
        width: 1px;
        height: 1px;
        border: none;
        opacity: 0;
      }

      /* Main content container */
      .main-content {
        width: 100%;
        height: 100vh;
        opacity: 0;
        transition: opacity 0.5s ease-in;
      }

      .main-content.visible {
        opacity: 1;
      }

      /* Responsive design */
      @media (max-width: 768px) {
        .loading-title {
          font-size: 2rem;
        }

        .loading-subtitle {
          font-size: 1rem;
        }

        .progress-bar {
          width: 250px;
        }
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
      <div class="loading-content">
        <h1 class="loading-title">PlantUML Editor</h1>
        <p class="loading-subtitle">Loading advanced diagram editor...</p>
        <div class="spinner"></div>
        <div class="progress-bar">
          <div class="progress-fill" id="progressFill"></div>
        </div>
        <p class="loading-status" id="loadingStatus">Initializing...</p>
      </div>
    </div>

    <!-- Hidden iframe to load PlantUML -->
    <iframe
      id="hiddenFrame"
      class="hidden-iframe"
      src="https://editor.plantuml.com/uml/"
      onload="handleIframeLoad()"
    >
    </iframe>

    <!-- Main content container -->
    <div class="main-content" id="mainContent">
      <!-- Content from remastered.html will be loaded here -->
    </div>

    <script>
      let loadingProgress = 0;
      let loadingInterval;
      let isLoaded = false;

      // Update loading progress
      function updateProgress(progress, status) {
        const progressFill = document.getElementById("progressFill");
        const loadingStatus = document.getElementById("loadingStatus");

        progressFill.style.width = progress + "%";
        loadingStatus.textContent = status;
      }

      // Simulate loading progress
      function startLoadingAnimation() {
        loadingInterval = setInterval(() => {
          if (loadingProgress < 90 && !isLoaded) {
            loadingProgress += Math.random() * 15;
            if (loadingProgress > 90) loadingProgress = 90;

            let status = "Initializing...";
            if (loadingProgress > 20) status = "Loading PlantUML editor...";
            if (loadingProgress > 50) status = "Preparing interface...";
            if (loadingProgress > 80) status = "Almost ready...";

            updateProgress(loadingProgress, status);
          }
        }, 200);
      }

      // Handle iframe load completion
      function handleIframeLoad() {
        if (isLoaded) return;
        isLoaded = true;

        clearInterval(loadingInterval);

        // Complete the progress
        updateProgress(100, "Loading complete!");

        // Wait a moment then load the remastered content
        setTimeout(() => {
          loadRemasteredContent();
        }, 500);
      }

      // Load content from remastered.html
      async function loadRemasteredContent() {
        try {
          updateProgress(100, "Loading local content...");

          const response = await fetch("./remastered.html");
          if (!response.ok) {
            throw new Error("Failed to load remastered.html");
          }

          const htmlContent = await response.text();

          // Extract the body content from remastered.html
          const parser = new DOMParser();
          const doc = parser.parseFromString(htmlContent, "text/html");

          // Get the main content
          const mainContent = document.getElementById("mainContent");
          mainContent.innerHTML = doc.body.innerHTML;

          // Copy over any styles from the head
          const headElements = doc.head.children;
          for (let element of headElements) {
            if (element.tagName === "STYLE" || element.tagName === "LINK") {
              document.head.appendChild(element.cloneNode(true));
            }
          }

          // Execute any scripts from remastered.html
          const scripts = doc.querySelectorAll("script");
          scripts.forEach((script) => {
            if (script.src) {
              // External script
              const newScript = document.createElement("script");
              newScript.src = script.src;
              newScript.async = script.async;
              newScript.defer = script.defer;
              document.head.appendChild(newScript);
            } else if (script.textContent) {
              // Inline script
              const newScript = document.createElement("script");
              newScript.textContent = script.textContent;
              document.head.appendChild(newScript);
            }
          });

          // Hide loading screen and show main content
          setTimeout(() => {
            const loadingScreen = document.getElementById("loadingScreen");
            const mainContent = document.getElementById("mainContent");

            loadingScreen.classList.add("hidden");
            mainContent.classList.add("visible");

            // Remove the loading screen after transition
            setTimeout(() => {
              loadingScreen.style.display = "none";
            }, 500);
          }, 300);
        } catch (error) {
          console.error("Error loading remastered content:", error);
          updateProgress(100, "Error loading content. Please refresh.");
        }
      }

      // Handle errors
      window.addEventListener("error", (event) => {
        console.error("Error occurred:", event.error);
      });

      // Start the loading process
      document.addEventListener("DOMContentLoaded", () => {
        startLoadingAnimation();

        // Fallback timeout in case iframe doesn't load
        setTimeout(() => {
          if (!isLoaded) {
            console.warn("Iframe load timeout, proceeding anyway...");
            handleIframeLoad();
          }
        }, 10000); // 10 second timeout
      });

      // Handle page visibility changes
      document.addEventListener("visibilitychange", () => {
        if (document.hidden) {
          // Page is hidden, pause any animations if needed
        } else {
          // Page is visible again
        }
      });
    </script>
  </body>
</html>
